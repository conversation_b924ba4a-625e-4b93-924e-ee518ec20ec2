package com.ict.datamanagement.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.car.AddCarRequest;
import com.ict.datamanagement.domain.dto.car.CarDownBox;
import com.ict.datamanagement.domain.dto.car.CarListRequest;
import com.ict.datamanagement.domain.dto.car.UpdateCarRequest;
import com.ict.datamanagement.domain.dto.car.actual.AddCarActualRequest;
import com.ict.datamanagement.domain.dto.car.actual.CarActualListRequest;
import com.ict.datamanagement.domain.dto.car.actual.UpdateCarActualRequest;
import com.ict.datamanagement.domain.entity.FileImportLogs;
import com.ict.datamanagement.domain.vo.carVO.AddCarDownBoxVO;
import com.ict.datamanagement.domain.vo.carVO.CarActualDownBoxVO;
import com.ict.datamanagement.domain.vo.carVO.CarActualVO;
import com.ict.datamanagement.domain.vo.carVO.CarVO;
import com.ict.datamanagement.service.CarService;
import com.ict.datamanagement.service.FileImportLogsService;
import com.ict.datamanagement.util.BeijingTimeUtil;
import com.ict.datamanagement.util.CsvImportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Api(tags = "车辆信息")
@Slf4j
@RestController("/car")
public class CarController {
    @Autowired
    private CarService carService;

    @Autowired
    private FileImportLogsService fileImportLogsService;

    @Autowired
    private CsvImportUtil csvImportUtil;

    @Value("${file.DOWNLOAD_NULL_FROM_PATH}")
    private String DOWNLOAD_NULL_FROM_PATH;

    @Value("${file.DOWNLOAD_PATH}")
    private String DOWNLOAD_PATH;

    //--------------搜索和列表展示
    @ApiOperation("搜索和列表展示")
    @GetMapping("/carList")
    public BaseResponse<Page<CarVO>> carList(CarListRequest carListRequest) {
        List<CarVO> carVOS = carService.getCarList(carListRequest);
        Integer pageNum = carListRequest.getPageNum();
        Integer pageSize = carListRequest.getPageSize();
        Page<CarVO> page = carService.getPage(pageNum, pageSize, carVOS);
        return ResultUtils.success(page);
    }

    @ApiOperation("搜索下拉框")
    @PostMapping("/selectDownBox")
    public BaseResponse<CarDownBox> selectDownBox() {
        CarDownBox res = carService.selectDownBox();
        return ResultUtils.success(res);
    }

    //-----------------------删除车辆
    @ApiOperation("删除车辆")
    @DeleteMapping("/deleteCar")
    public String deleteCar(Long carId) {
        String info = String.valueOf(carService.deleteCar(carId));
        return info;
    }

    //--------------------添加车辆
    @ApiOperation("添加车辆")
    @PostMapping("/addCar")
    public BaseResponse addCar(AddCarRequest addCarRequest) {
        String s = carService.addCar(addCarRequest);
        return ResultUtils.success(s);
    }

    @ApiOperation("获取添加车辆下拉框")
    @PostMapping("/getAddCarDownBox")
    public BaseResponse<AddCarDownBoxVO> getAddCarDownBox() {
        AddCarDownBoxVO addCarDownBoxVO = carService.getCarDownBox();
        return ResultUtils.success(addCarDownBoxVO);
    }

    //----------------------修改车辆
    @ApiOperation("修改车辆")
    @PostMapping("/updateCar")
    public BaseResponse updateCar(UpdateCarRequest updateCarRequest) {
        int i = carService.updateCar(updateCarRequest);
        if (i == 1) {
            return ResultUtils.success("修改成功");
        }
        return ResultUtils.error(StatusCode.valueOf("修改失败"));
    }

    //=========================工作实情===============================
    @ApiOperation("工作实情列表")
    @GetMapping("/carActualList")
    public BaseResponse<Page<CarActualVO>> carActualList(CarActualListRequest carActualListRequest) {
        List<CarActualVO> CarActualVOS = carService.getCarActualVOList(carActualListRequest);
        Integer pageNum = carActualListRequest.getPageNum();
        Integer pageSize = carActualListRequest.getPageSize();
        Page<CarActualVO> page = carService.getActualPage(pageNum, pageSize, CarActualVOS);
        return ResultUtils.success(page);
    }

    //---------添加工作实情
    @ApiOperation("添加工作实情")
    @PostMapping("/addCarActualList")
    public BaseResponse addActual(AddCarActualRequest addCarActualRequest) {
        String info = carService.addCarActual(addCarActualRequest);
        return ResultUtils.success(info);
    }

    //----------------------下拉框
    @ApiOperation("添加工作实情下拉框")
    @PostMapping("/carActualDownBox")
    public BaseResponse<CarActualDownBoxVO> getCarActualDownBox() {
        CarActualDownBoxVO cadbv = carService.getCarActualDownBox();
        return ResultUtils.success(cadbv);
    }

    //-----------------修改实情
    @ApiOperation("修改实情")
    @PostMapping("/updateCarActual")
    public BaseResponse updateCarActual(UpdateCarActualRequest updateCarActualRequest) {
        int i = carService.updateCarActual(updateCarActualRequest);
        if (i == 1) {
            return ResultUtils.success("修改成功");
        }
        return ResultUtils.error(StatusCode.valueOf("修改失败"));
    }

    //---------------------删除实情
    @ApiOperation("删除实情")
    @DeleteMapping("/deleteCarActual")
    public BaseResponse deleteCarActual(Long carId) {
        int info = carService.deleteCarActual(carId);
        if (info == 1) {
            return ResultUtils.success("删除成功");
        }
        return ResultUtils.error(StatusCode.valueOf("删除失败"));
    }

    //---------------------csv格式文件导入导出
    @ApiOperation("导入车辆实情")
    @PostMapping(value = "/carImport")
    public String csvImport(@RequestParam("File") MultipartFile file, @RequestHeader("Authorization") String authorization) {
        String contentType = file.getContentType();
        File File = csvImportUtil.uploadFile(file);
        String beijingTime = "";
        String info = "文件格式不正确";
        try {
            beijingTime = BeijingTimeUtil.getBeijingTime();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            String regex = "^(\\d{4})/(0[1-9]|[1-9]|1[0-2])/([1-9]|0[1-9]|[12]\\d|3[01])$";
            // 编译正则表达式
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(beijingTime);
            //校验日期是否合法
            if (!StringUtil.isNotBlank(beijingTime) || !matcher.matches()) {
                // 设置时区为东八区
                ZoneId zoneId = ZoneId.of("Asia/Shanghai");

                // 获取东八区的当前时间
                ZonedDateTime now = ZonedDateTime.now(zoneId);

                // 设置时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

                // 格式化时间
                beijingTime = now.format(formatter);
            }

            if ("text/csv".equals(contentType)) {
                //处理csv格式文件
                System.out.println("date1: " + beijingTime);
                // 使用CSV工具类，生成file文件
                //上传文件
                boolean flag = carService.checkExportFrom(File, contentType);
                if (!flag) {
                    info = "导入失败,csv文件中的格式与模板不符";
                } else {
                    info = carService.readCsv(File, beijingTime, authorization);
                }
            } else if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType) || "application/vnd.ms-excel".equals(contentType)) {
                //处理xlsx，xls文件
                boolean flag = carService.checkExportFrom(File, contentType);
                if (!flag) {
                    info = "导入失败,Excel文件中的格式与模板不符";
                } else {
                    info = carService.exportExcel(beijingTime, File, authorization);
                }
            }
        }
        return info;
    }

    //导入日志
    @ApiOperation("导入日志")
    @GetMapping("/getImportLogs")
    public BaseResponse<Page<FileImportLogs>> getImportLogs(@RequestParam int pageNum, @RequestParam int pageSize,@ApiParam(value = "0表示该表格是商铺表，1表示该表格是车辆实情表,2取货户分析，3选址分析", required = true) @RequestParam String type) {
        Page<FileImportLogs> page = fileImportLogsService.page(new Page<>(pageNum, pageSize), new QueryWrapper<FileImportLogs>().eq("store_or_car", type));
        return ResultUtils.success(page);
    }

    //删除日志
    @ApiOperation("删除日志")
    @DeleteMapping("/deleteImportLogs")
    public BaseResponse deleteImportLogs(Long logsId) {
        int i = fileImportLogsService.deleteImportLogs(logsId);
        if (i == 1) {
            return ResultUtils.success("删除成功");
        }
        return ResultUtils.error(StatusCode.valueOf("删除失败"));
    }


    //下载空白表格
    @ApiOperation("下载空白表格")
    @PostMapping("/downloadNullFrom")

    public void downloadNullFrom(@ApiParam(name = "code", value = "0表示商铺，1表示车辆")
                                 @RequestParam int code,
                                 @ApiParam(name = "csvOrExcel", value = "0表示csv文件，1表示xlsx文件, 2表示xls文件")
                                 @RequestParam int csvOrExcel,
                                 HttpServletResponse response) {
        //code:0商铺，1车辆.csvOrExcel:0标识csv文件，1标识xlsx文件,2标识xls文件
        System.out.println(1);
        String fileName = "";
        if (code == 0) {
            if (csvOrExcel == 0) {
                fileName = "商铺信息导入模板.csv";
            } else if (csvOrExcel == 1) {
                fileName = "商铺信息导入模板.xlsx";
            } else {
                fileName = "商铺信息导入模板.xls";
            }
        } else {
            if (csvOrExcel == 0) {
                fileName = "实情信息导入模板.csv";
            } else if (csvOrExcel == 1) {
                fileName = "实情信息导入模板.xlsx";
            } else {
                fileName = "实情信息导入模板.xls";
            }
        }

        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 获取文件路径
            String filePath = "data/nullFrom/" + fileName;
            ClassPathResource resource = new ClassPathResource(filePath);

            if (resource.exists()) {
                // 如果文件存在，直接下载
                try (InputStream inputStream = resource.getInputStream();
                     OutputStream outputStream = response.getOutputStream()) {
                    StreamUtils.copy(inputStream, outputStream);
                    outputStream.flush();
                }
            } else {
                // 如果文件不存在，返回错误信息
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"模板文件不存在\"}");
            }
        } catch (Exception e) {
            e.printStackTrace();
            try {
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"下载失败: " + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }

        String path = DOWNLOAD_NULL_FROM_PATH + fileName;
        try {
            // path是指想要下载的文件的路径
            File file = new File(path);
            log.info(file.getPath());
            // 获取文件名
            String filename = file.getName();
            System.out.println("fileName: " + filename);
            // 获取文件后缀名
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
            log.info("文件后缀名：" + ext);
            // 将文件写入输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            InputStream fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            //Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
            //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            // 告知浏览器文件的大小
            response.addHeader("Content-Length", "" + file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }


    @ApiOperation("下载日志")
    @GetMapping("/downloadLogs")
    public void downloadLogs(String fileName, String importTime, HttpServletResponse response) {
        //构建文件下载路径
        String[] split = importTime.split(" ");
        String datePart = split[0];// 2024/10/8
        String[] split1 = datePart.split("/");
        String s = String.valueOf(Integer.parseInt(split1[1]));
        String s1 = String.valueOf(Integer.parseInt(split1[2]));
        datePart = split1[0] + "/" + s + "/" + s1;
        String timePart = split[1];
        String[] timeSplit = timePart.split(":");
        //String path = "/www/wwwroot/ycwl/ycwlms/data-management/data/Download/" + datePart + "/";
        String path = DOWNLOAD_PATH + datePart + "/";
        path += timeSplit[0] + "/" + timeSplit[1] + "/" + timeSplit[2] + "/";
        path += fileName;
        try {
            // path是指想要下载的文件的路径
            File file = new File(path);
            log.info(file.getPath());
            // 获取文件名
            String filename = file.getName();
            System.out.println("fileName: " + filename);
            // 获取文件后缀名
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
            log.info("文件后缀名：" + ext);
            // 将文件写入输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            InputStream fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            //Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
            //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            // 告知浏览器文件的大小
            response.addHeader("Content-Length", "" + file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

}
